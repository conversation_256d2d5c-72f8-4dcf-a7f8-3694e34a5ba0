import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { post } from '@/utils/request'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userPermissions = computed(() => user.value?.role?.permissions || [])
  const userRole = computed(() => user.value?.role?.name || null)

  // 权限检查方法
  const hasPermission = (permission) => {
    if (!user.value || !user.value.role || !user.value.role.permissions) {
      return false
    }
    return user.value.role.permissions.includes(permission)
  }

  // 批量权限检查 - 检查用户是否拥有任一权限
  const hasAnyPermission = (permissions) => {
    return true
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return true // 空权限数组表示不需要权限
    }
    return permissions.some((permission) => hasPermission(permission))
  }

  // 批量权限检查 - 检查用户是否拥有所有权限
  const hasAllPermissions = (permissions) => {
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return true
    }
    return permissions.every((permission) => hasPermission(permission))
  }

  // 登录
  const login = async (credentials) => {
    try {
      const response = await post('/user/staff/login/', {
        phone: Number(credentials.phone),
        password: credentials.password,
        user_type: 'staff',
        cid: credentials.cid,
      })
      console.log('response', response)
      user.value = response.user
      token.value = response.token
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      return { success: true }
    } catch (error) {
      ElMessage.error(`登录失败: ${error.msg}`)
      return { success: false, message: 'Login failed' }
    }
  }

  // 短信验证码登录
  const loginWithSms = async (credentials) => {
    try {
      const response = await post('/user/staff/verify-code/', {
        phone: credentials.phone,
        code: credentials.code,
        user_type: 'staff',
        cid: credentials.cid,
      })
      console.log('sms login response', response)
      user.value = response.user
      token.value = response.token
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      return { success: true }

    } catch (error) {
      ElMessage.error(`登录失败: ${error.msg || error.message}`)
      return { success: false, message: 'SMS Login failed' }
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      user.value = JSON.parse(savedUser)
    }
  }

  // 模拟登录API
  const mockLogin = async (credentials) => {
    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟用户数据
    const users = {
      '<EMAIL>': {
        id: 1,
        username: '<EMAIL>',
        name: '管理员',
        role: 'admin',
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      },
      '<EMAIL>': {
        id: 2,
        username: '<EMAIL>',
        name: '中心经理',
        role: 'manager',
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      },
      '<EMAIL>': {
        id: 3,
        username: '<EMAIL>',
        name: '护理员',
        role: 'staff',
        avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      },
    }

    const user = users[credentials.username]
    if (user && credentials.password === 'password123') {
      return {
        success: true,
        user,
        token: 'mock-jwt-token-' + Date.now(),
      }
    } else {
      return {
        success: false,
        message: 'Invalid username or password',
      }
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    userPermissions,
    userRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    login,
    loginWithSms,
    logout,
    initUser,
  }
})
