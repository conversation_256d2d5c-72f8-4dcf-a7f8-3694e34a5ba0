<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑待办事项' : '新增待办事项'"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事项类型" prop="todo_type">
            <el-select
              v-model="formData.todo_type"
              placeholder="请选择事项类型"
              style="width: 100%"
            >
              <el-option
                v-for="option in todoTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="执行人" prop="assigned_to">
            <el-select
              v-model="formData.assigned_to"
              placeholder="请选择执行人"
              style="width: 100%"
              filterable
              :loading="staffLoading"
            >
              <el-option
                v-for="staff in staffOptions"
                :key="staff.sid"
                :label="staff.name"
                :value="staff.sid"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="关联产妇" prop="maternity_admission">
            <el-select
              v-model="formData.maternity_admission"
              placeholder="请选择关联产妇（可选）"
              style="width: 100%"
              filterable
              clearable
              :loading="maternityLoading"
            >
              <el-option
                v-for="maternity in maternityOptions"
                :key="maternity.aid"
                :label="maternity.maternity"
                :value="maternity.aid"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事项内容" prop="todo_content">
            <el-input
              v-model="formData.todo_content"
              type="textarea"
              :rows="4"
              placeholder="请输入事项内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注信息" prop="todo_remark">
            <el-input
              v-model="formData.todo_remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/request.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  todoData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'success'])

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const staffLoading = ref(false)
const maternityLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = ref({
  todo_type: '',
  assigned_to: '',
  maternity_admission: '',
  todo_content: '',
  todo_remark: ''
})

// 选项数据
const staffOptions = ref([])
const maternityOptions = ref([])

// 待办事项类型选项
const todoTypeOptions = [
  { value: 'FEEDBACK', label: '客户反馈' },
  { value: 'MAINTENANCE', label: '设备维护' },
  { value: 'INSPECTION', label: '检查任务' },
  { value: 'OTHER', label: '其他' }
]

// 表单验证规则
const formRules = {
  todo_type: [
    { required: true, message: '请选择事项类型', trigger: 'change' }
  ],
  assigned_to: [
    { required: true, message: '请选择执行人', trigger: 'change' }
  ],
  todo_content: [
    { required: true, message: '请输入事项内容', trigger: 'blur' },
    { min: 1, max: 500, message: '事项内容长度在 1 到 500 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(visible, (newValue) => {
  if (newValue) {
    initForm()
    fetchStaffOptions()
    fetchMaternityOptions()
  } else {
    resetForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.isEdit && props.todoData) {
    formData.value = {
      todo_type: props.todoData.todo_type || '',
      assigned_to: props.todoData.assigned_to || '',
      maternity_admission: props.todoData.maternity_admission || '',
      todo_content: props.todoData.todo_content || '',
      todo_remark: props.todoData.todo_remark || ''
    }
  } else {
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    todo_type: '',
    assigned_to: '',
    maternity_admission: '',
    todo_content: '',
    todo_remark: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 获取员工选项
const fetchStaffOptions = async () => {
  staffLoading.value = true
  try {
    const response = await get('todo/staff/list/')
    staffOptions.value = response || []
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
  } finally {
    staffLoading.value = false
  }
}

// 获取产妇选项
const fetchMaternityOptions = async () => {
  maternityLoading.value = true
  try {
    const response = await get('todo/maternity/list/')
    maternityOptions.value = response || []
  } catch (error) {
    console.error('获取产妇列表失败:', error)
    ElMessage.error('获取产妇列表失败')
  } finally {
    maternityLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const submitData = {
      ...formData.value
    }

    // 如果没有选择产妇，删除该字段
    if (!submitData.maternity_admission) {
      delete submitData.maternity_admission
    }

    if (props.isEdit) {
      // 更新
      await put(`todo/assign/update/${props.todoData.rid}/`, submitData)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await post('todo/assign/create/', submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    if (error.name !== 'ValidationError') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
