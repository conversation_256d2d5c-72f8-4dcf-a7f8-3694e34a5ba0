<template>
  <div class="">
    <!-- 主要内容 -->
    <div class="relative z-10">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">运营总览</h1>
        <p class="text-gray-600">实时监控月子中心运营状况</p>
      </div>

      <!-- 快捷菜单 -->
      <QuickMenuGrid />

      <!-- 统计卡片 -->
      <StatisticsCards :data="dashboardData" :loading="loading" />

      <!-- 图表区域 -->
      <ChartsSection :data="dashboardData" :loading="loading" />

      <!-- 待办事项和提醒 -->
      <TasksAndReminders />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { get } from '@/utils/request'
import QuickMenuGrid from '@/components/dashboard/QuickMenuGrid.vue'
import StatisticsCards from '@/components/dashboard/StatisticsCards.vue'
import ChartsSection from '@/components/dashboard/ChartsSection.vue'
import TasksAndReminders from '@/components/dashboard/TasksAndReminders.vue'

const dashboardData = ref(null)
const loading = ref(false)

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const data = await get('overview/data', {}, { showError: true })
    dashboardData.value = data
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped></style>
