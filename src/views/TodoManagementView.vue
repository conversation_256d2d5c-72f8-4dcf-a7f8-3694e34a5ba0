<template>
  <div class="todo-management-view">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">待办事项管理</h1>
          <p class="text-gray-600 mt-2">管理和跟踪所有待办事项</p>
        </div>
        <el-button
          type="primary"
          @click="handleCreate"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-2">
            <Plus />
          </el-icon>
          新增待办事项
        </el-button>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel
      :fields="filterFields"
      :filters="filters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 待办事项列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
          @row-click="handleRowClick"
          class="todo-table"
        >
          <el-table-column prop="created_at" label="创建时间"  align="center">
            <template #default="{ row }">
              <span>{{ row.created_at }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="todo_type_display" label="事项类型"  align="center">
            <template #default="{ row }">
              <el-tag :type="getTodoTypeTagType(row.todo_type)" size="small">
                {{ row.todo_type_display }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="assign_name" label="指派人"  align="center" />
          
          <el-table-column prop="assigned_to_name" label="执行人"  align="center" />

          <el-table-column prop="todo_content" label="事项内容" min-width="120" align="center">
            <template #default="{ row }">
              <div class="text-center">
                <p class="text-sm text-gray-800 line-clamp-2">{{ row.todo_content }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="maternity" label="关联产妇"  align="center">
            <template #default="{ row }">
              <span>{{ row.maternity || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="todo_status_display" label="状态"  align="center">
            <template #default="{ row }">
              <el-tag :type="getTodoStatusTagType(row.todo_status)" size="small">
                {{ row.todo_status_display }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="complete_time" label="完成时间"  align="center">
            <template #default="{ row }">
              <span>{{ row.complete_time || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
              <div class="flex justify-center gap-1">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleViewDetail(row)"
                  class="bg-blue-500 hover:bg-blue-600 border-blue-500"
                >
                  查看
                </el-button>
                <el-button
                  v-if="row.is_editable"
                  type="warning"
                  size="small"
                  @click.stop="handleEdit(row)"
                  class="bg-orange-500 hover:bg-orange-600 border-orange-500"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="row.is_editable"
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(row)"
                  class="bg-red-500 hover:bg-red-600 border-red-500"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination-pink"
          />
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <TodoDetailDialog
      v-model="detailDialogVisible"
      :todo-id="selectedTodoId"
      @close="handleDetailDialogClose"
      @edit="handleEditFromDetail"
      @delete="handleDeleteFromDetail"
    />

    <!-- 创建/编辑对话框 -->
    <TodoFormDialog
      v-model="formDialogVisible"
      :todo-data="selectedTodoData"
      :is-edit="isEdit"
      @close="handleFormDialogClose"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import FilterPanel from '@/components/FilterPanel.vue'
import TodoDetailDialog from '@/components/TodoDetailDialog.vue'
import TodoFormDialog from '@/components/TodoFormDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 详情对话框
const detailDialogVisible = ref(false)
const selectedTodoId = ref('')

// 表单对话框
const formDialogVisible = ref(false)
const selectedTodoData = ref(null)
const isEdit = ref(false)

// 筛选条件
const filters = reactive({
  sk: '',
  todo_status: '',
  todo_type: ''
})

// 待办事项状态选项
const todoStatusOptions = [
  { value: 'PENDING', label: '待办' },
  { value: 'IN_PROGRESS', label: '进行中' },
  { value: 'COMPLETED', label: '已完成' }
]

// 待办事项类型选项
const todoTypeOptions = [
  { value: 'FEEDBACK', label: '客户反馈' },
  { value: 'COMPLAINT', label: '客户投诉' },
  { value: 'DAILY_CLEANING', label: '日常清洁' },
  { value: 'CUSTOMER_CARE', label: '客户关怀' },
  { value: 'CUSTOMER_VISIT', label: '客户回访' },
  { value: 'OTHER', label: '其他' }
]

// 筛选字段配置
const filterFields = [
  {
    key: 'sk',
    label: '搜索',
    type: 'input',
    placeholder: '搜索事项内容、指派人、执行人',
    autoSearch: true // 启用自动搜索
  },
  {
    key: 'todo_status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: todoStatusOptions,
    autoSearch: true // 启用自动搜索
  },
  {
    key: 'todo_type',
    label: '事项类型',
    type: 'select',
    placeholder: '请选择类型',
    options: todoTypeOptions,
    autoSearch: true // 启用自动搜索
  }
]

// 获取待办事项状态标签类型
const getTodoStatusTagType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success'
  }
  return statusMap[status] || 'default'
}

// 获取待办事项类型标签类型
const getTodoTypeTagType = (type) => {
  const typeMap = {
    'FEEDBACK': 'primary',
    'COMPLAINT': 'danger',
    'DAILY_CLEANING': 'success',
    'CUSTOMER_CARE': 'warning',
    'CUSTOMER_VISIT': 'info',
    'OTHER': 'default'
  }
  return typeMap[type] || 'default'
}

// 获取待办事项列表
const fetchTodoList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...filters
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('todo/assign/list/', params)
    tableData.value = response.list || []
    totalCount.value = response.total_count || 0
  } catch (error) {
    console.error('获取待办事项列表失败:', error)
    ElMessage.error('获取待办事项列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchTodoList()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchTodoList()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTodoList()
}

// 行点击处理
const handleRowClick = (row) => {
  handleViewDetail(row)
}

// 查看详情
const handleViewDetail = (row) => {
  selectedTodoId.value = row.rid
  detailDialogVisible.value = true
}

// 详情对话框关闭
const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedTodoId.value = ''
}

// 创建待办事项
const handleCreate = () => {
  selectedTodoData.value = null
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑待办事项
const handleEdit = async (row) => {
  try {
    // 先获取详情数据
    const detailData = await get(`todo/assign/detail/${row.rid}/`)
    selectedTodoData.value = detailData
    isEdit.value = true
    formDialogVisible.value = true
  } catch (error) {
    console.error('获取待办事项详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 删除待办事项
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除待办事项"${row.todo_content}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await del(`todo/assign/delete/${row.rid}/`)
    ElMessage.success('删除成功')

    // 刷新列表
    fetchTodoList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除待办事项失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单对话框关闭
const handleFormDialogClose = () => {
  formDialogVisible.value = false
  selectedTodoData.value = null
  isEdit.value = false
}

// 表单提交成功
const handleFormSuccess = () => {
  handleFormDialogClose()
  fetchTodoList()
}

// 从详情对话框编辑
const handleEditFromDetail = (todoData) => {
  // 关闭详情对话框
  handleDetailDialogClose()
  // 直接使用详情数据打开编辑对话框（详情对话框已经有完整数据）
  selectedTodoData.value = { ...todoData }
  isEdit.value = true
  formDialogVisible.value = true
}

// 从详情对话框删除
const handleDeleteFromDetail = async (todoData) => {
  // 关闭详情对话框
  handleDetailDialogClose()
  // 执行删除操作
  await handleDelete(todoData)
}

// 初始化
onMounted(() => {
  fetchTodoList()
})
</script>

<style scoped>
.todo-management-view {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.todo-table :deep(.el-table__row) {
  cursor: pointer;
}

.todo-table :deep(.el-table__row:hover) {
  background-color: #fdf2f8;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 分页样式 */
.pagination-pink :deep(.el-pagination__total),
.pagination-pink :deep(.el-pagination__sizes),
.pagination-pink :deep(.el-pagination__jump) {
  color: #6b7280;
}

.pagination-pink :deep(.el-pagination .btn-next),
.pagination-pink :deep(.el-pagination .btn-prev) {
  color: #ec4899;
}

.pagination-pink :deep(.el-pagination .btn-next:hover),
.pagination-pink :deep(.el-pagination .btn-prev:hover) {
  color: #be185d;
}

.pagination-pink :deep(.el-pager li.is-active) {
  background-color: #ec4899;
  border-color: #ec4899;
}

.pagination-pink :deep(.el-pager li:hover) {
  color: #ec4899;
}
</style>
