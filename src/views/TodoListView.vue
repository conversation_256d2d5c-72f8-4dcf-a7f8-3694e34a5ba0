<template>
  <div class="todo-list-view">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">待办事项</h1>
          <p class="text-gray-600 mt-2">查看和处理分配给我的待办事项</p>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel
      :fields="filterFields"
      :filters="filters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 待办事项列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
          @row-click="handleRowClick"
          class="todo-table"
        >
          <el-table-column prop="created_at" label="创建时间" align="center">
            <template #default="{ row }">
              <span>{{ row.created_at }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="todo_type_display" label="事项类型" align="center">
            <template #default="{ row }">
              <el-tag :type="getTodoTypeTagType(row.todo_type)" size="small">
                {{ row.todo_type_display }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="assign_name" label="指派人" align="center" />

          <el-table-column prop="todo_content" label="事项内容" min-width="120" align="center">
            <template #default="{ row }">
              <div class="text-center">
                <p class="text-sm text-gray-800 line-clamp-2">{{ row.todo_content }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="maternity" label="关联产妇" align="center">
            <template #default="{ row }">
              <span>{{ row.maternity || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="todo_status_display" label="状态" align="center">
            <template #default="{ row }">
              <el-tag :type="getTodoStatusTagType(row.todo_status)" size="small">
                {{ row.todo_status_display }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="complete_time" label="完成时间" align="center">
            <template #default="{ row }">
              <span>{{ row.complete_time || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="{ row }">
              <div class="flex justify-center gap-1">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="handleViewDetail(row)"
                  class="bg-blue-500 hover:bg-blue-600 border-blue-500"
                >
                  查看
                </el-button>
                <el-button
                  v-if="row.todo_status === 'IN_PROGRESS'"
                  type="success"
                  size="small"
                  @click.stop="handleComplete(row)"
                  class="bg-green-500 hover:bg-green-600 border-green-500"
                >
                  完成
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination-pink"
          />
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <TodoDetailDialog
      v-model="detailDialogVisible"
      :todo-id="selectedTodoId"
      @close="handleDetailDialogClose"
    />

    <!-- 完成对话框 -->
    <TodoCompleteDialog
      v-model="completeDialogVisible"
      :todo-data="selectedTodoData"
      @close="handleCompleteDialogClose"
      @success="handleCompleteSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'
import FilterPanel from '@/components/FilterPanel.vue'
import TodoDetailDialog from '@/components/TodoDetailDialog.vue'
import TodoCompleteDialog from '@/components/TodoCompleteDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 详情对话框
const detailDialogVisible = ref(false)
const selectedTodoId = ref('')

// 完成对话框
const completeDialogVisible = ref(false)
const selectedTodoData = ref(null)

// 筛选条件
const filters = reactive({
  sk: '',
  todo_status: '',
  todo_type: ''
})

// 待办事项状态选项
const todoStatusOptions = [
  { value: 'PENDING', label: '待办' },
  { value: 'IN_PROGRESS', label: '进行中' },
  { value: 'COMPLETED', label: '已完成' }
]

// 待办事项类型选项
const todoTypeOptions = [
  { value: 'FEEDBACK', label: '客户反馈' },
  { value: 'COMPLAINT', label: '客户投诉' },
  { value: 'DAILY_CLEANING', label: '日常清洁' },
  { value: 'CUSTOMER_CARE', label: '客户关怀' },
  { value: 'CUSTOMER_VISIT', label: '客户回访' },
  { value: 'OTHER', label: '其他' }
]

// 筛选字段配置
const filterFields = [
  {
    key: 'sk',
    label: '搜索',
    type: 'input',
    placeholder: '搜索事项内容、指派人',
    autoSearch: true
  },
  {
    key: 'todo_status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: todoStatusOptions,
    autoSearch: true
  },
  {
    key: 'todo_type',
    label: '事项类型',
    type: 'select',
    placeholder: '请选择类型',
    options: todoTypeOptions,
    autoSearch: true
  }
]

// 获取待办事项状态标签类型
const getTodoStatusTagType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success'
  }
  return statusMap[status] || 'default'
}

// 获取待办事项类型标签类型
const getTodoTypeTagType = (type) => {
  const typeMap = {
    'FEEDBACK': 'primary',
    'COMPLAINT': 'danger',
    'DAILY_CLEANING': 'success',
    'CUSTOMER_CARE': 'warning',
    'CUSTOMER_VISIT': 'info',
    'OTHER': 'default'
  }
  return typeMap[type] || 'default'
}

// 获取待办事项列表
const fetchTodoList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...filters
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('todo/list', params)
    tableData.value = response.list || []
    totalCount.value = response.total_count || 0
  } catch (error) {
    console.error('获取待办事项列表失败:', error)
    ElMessage.error('获取待办事项列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchTodoList()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchTodoList()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTodoList()
}

// 行点击处理
const handleRowClick = (row) => {
  handleViewDetail(row)
}

// 查看详情
const handleViewDetail = (row) => {
  selectedTodoId.value = row.rid
  detailDialogVisible.value = true
}

// 完成待办事项
const handleComplete = (row) => {
  selectedTodoData.value = { ...row }
  completeDialogVisible.value = true
}

// 详情对话框关闭
const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedTodoId.value = ''
}

// 完成对话框关闭
const handleCompleteDialogClose = () => {
  completeDialogVisible.value = false
  selectedTodoData.value = null
}

// 完成成功
const handleCompleteSuccess = () => {
  handleCompleteDialogClose()
  fetchTodoList()
}

// 初始化
onMounted(() => {
  fetchTodoList()
})
</script>

<style scoped>
.todo-list-view {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.todo-table :deep(.el-table__row) {
  cursor: pointer;
}

.todo-table :deep(.el-table__row:hover) {
  background-color: #fdf2f8;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 分页样式 */
.pagination-pink :deep(.el-pagination__total),
.pagination-pink :deep(.el-pagination__sizes),
.pagination-pink :deep(.el-pagination__jump) {
  color: #6b7280;
}

.pagination-pink :deep(.el-pagination .btn-next),
.pagination-pink :deep(.el-pagination .btn-prev) {
  color: #ec4899;
}

.pagination-pink :deep(.el-pagination .btn-next:hover),
.pagination-pink :deep(.el-pagination .btn-prev:hover) {
  color: #be185d;
}

.pagination-pink :deep(.el-pager li.is-active) {
  background-color: #ec4899;
  border-color: #ec4899;
}

.pagination-pink :deep(.el-pager li:hover) {
  color: #ec4899;
}
</style>
