<template>
  <div class="duty-list-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Clock />
          </el-icon>
          值班列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条值班记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="dutyData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="schedule_date" label="值班日期" min-width="120" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <span class="font-medium">{{ row.schedule_date }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="staff_name" label="员工姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <span class="font-medium">{{ row.staff_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="staff_phone" label="联系电话" min-width="130">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <span class="text-sm text-gray-700">{{ row.staff_phone }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="department_name" label="部门" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-700 font-medium">{{ row.department_name }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="position" label="岗位" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-700 font-medium">{{ row.position }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="shift_type" label="班次" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getShiftTypeTagType(row.shift_type)" size="small">
            {{ row.shift_type_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getOrgScheduleStatusTagType(row.status)" size="small">
            {{ row.status_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="备注" min-width="150">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.remark || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              size="small"
              @click="handleView(row)"
              class="bg-gray-500 hover:bg-gray-600 border-gray-500 hover:border-gray-600"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click.stop="handleEdit(row)"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
            >
              编辑
            </el-button>
            <el-button type="danger" size="small" @click.stop="handleDelete(row)"> 删除 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { Clock } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import { getShiftTypeTagType, getOrgScheduleStatusTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

// 定义属性
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['edit', 'delete', 'pagination-change', 'view'])

// 内部状态管理
const loading = ref(false)
const dutyData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载值班数据
const loadDutyData = async () => {
  loading.value = true
  try {
    // 处理筛选参数，将 duty_date 转换为 start_date 和 end_date
    const processedFilters = { ...props.filters }
    if (processedFilters.duty_date) {
      processedFilters.start_date = processedFilters.duty_date
      processedFilters.end_date = processedFilters.duty_date
      delete processedFilters.duty_date
    }

    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...processedFilters,
    }

    const response = await get('organizational-management/schedule/list/', params)

    // 直接使用后端数据，不做转换
    if (response?.list) {
      dutyData.value = response.list
      totalCount.value = response.total_count
    } else {
      dutyData.value = []
      totalCount.value = 0
    }
  } catch (error) {
    console.error('获取值班数据失败:', error)
    dutyData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 行点击处理
const handleRowClick = (row) => {
  handleView(row)
}

// 查看值班详情
const handleView = (duty) => {
  emit('view', duty)
}

// 编辑值班
const handleEdit = (duty) => {
  emit('edit', duty)
}

// 删除值班
const handleDelete = async (duty) => {
  try {
    await ElMessageBox.confirm('确定要删除此值班记录吗？删除后无法恢复。', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    })

    loading.value = true

    await del(`organizational-management/schedule/delete/${duty.rid}/`)

    ElMessage.success('删除成功')

    // 刷新当前页数据
    refresh()

    emit('delete', duty.id)
  } catch (error) {
    showErrorTip(error)
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  loadDutyData()
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadDutyData()
  emit('pagination-change', { page, size: pageSize.value })
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadDutyData()
}

// 刷新当前页数据
const refresh = () => {
  loadDutyData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

onMounted(() => {
  loadDutyData()
})
</script>

<style scoped>
.duty-list-container {
  transition: all 0.3s ease;
  width: 100%;
}

.duty-list-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  background-color: #f9fafb;
}
</style>
