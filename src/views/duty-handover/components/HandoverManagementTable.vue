<template>
  <div
    class="handover-management-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Management />
          </el-icon>
          交接班报告管理列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @selection-change="handleSelectionChange"
    >
      <!-- 复选框列 -->
      <el-table-column type="selection" width="55" fixed="left" />

      <el-table-column prop="rid" label="交班编号" min-width="170" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-blue-600 cursor-pointer" @click="handleView(row)">
            {{ row.rid }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="staff_department_name" label="交班部门" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-icon class="mr-2 text-pink-500">
              <House />
            </el-icon>
            <span class="font-medium">{{ row.staff_department_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="staff_name" label="交班人" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.staff_name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.staff_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="schedule_date" label="值班日期" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.schedule_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="shift_time" label="交班时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.shift_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.shift_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="schedule_shift_type_display" label="班次" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getShiftTypeTagType(row.schedule_shift_type)" size="small" effect="light">
            {{ row.schedule_shift_type_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="audit_status" label="审核状态" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getAuditStatusTagType(row.audit_status)" size="small" effect="light">
            <el-icon class="mr-1">
              <component :is="getAuditStatusIcon(row.audit_status)" />
            </el-icon>
            {{ getAuditStatusDisplay(row.audit_status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="auditor_name" label="审核人" min-width="100">
        <template #default="{ row }">
          <span v-if="row.auditor_name" class="text-gray-700">{{ row.auditor_name }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="audit_time" label="审核时间" min-width="160">
        <template #default="{ row }">
          <div v-if="row.audit_time" class="text-sm text-gray-600">
            <div>{{ formatDate(row.audit_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.audit_time) }}</div>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="work_summary" label="工作总结" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip
            :content="row.work_summary"
            placement="top"
            :disabled="row.work_summary.length <= 50"
          >
            <span class="text-gray-700">
              {{
                row.work_summary.length > 50
                  ? row.work_summary.substring(0, 50) + '...'
                  : row.work_summary
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.created_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons space-x-2">
            <el-button
              type="default"
              size="small"
              class="text-blue-600 border-blue-200 hover:bg-blue-50"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.audit_status === 'pending'"
              type="default"
              size="small"
              class="text-green-600 border-green-200 hover:bg-green-50"
              @click="handleAudit(row)"
            >
              审核
            </el-button>
            <el-button
              type="default"
              size="small"
              class="text-red-600 border-red-200 hover:bg-red-50"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && tableData.length === 0" class="text-center py-16">
      <div class="text-gray-400 text-6xl mb-4">
        <el-icon><Management /></el-icon>
      </div>
      <p class="text-gray-500 text-lg">暂无交接班记录</p>
      <p class="text-gray-400 text-sm mt-2">符合筛选条件的记录为空</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Management,
  House,
  Clock,
  CircleCheck,
  CircleClose,
  Warning,
} from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getShiftTypeTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const emit = defineEmits([
  'view',
  'edit',
  'delete',
  'audit',
  'selection-change',
  'statistics-change',
])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 获取审核状态标签类型
const getAuditStatusTagType = (status) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取审核状态图标
const getAuditStatusIcon = (status) => {
  const iconMap = {
    pending: Warning,
    approved: CircleCheck,
    rejected: CircleClose,
  }
  return iconMap[status] || Clock
}

// 获取审核状态显示文本
const getAuditStatusDisplay = (status) => {
  const statusMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '未通过',
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    return format(new Date(dateString), 'yyyy-MM-dd')
  } catch (error) {
    return dateString
  }
}

// 格式化时间
const formatTime = (dateString) => {
  if (!dateString) return ''
  try {
    return format(new Date(dateString), 'HH:mm:ss')
  } catch (error) {
    return dateString
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的shift_date字段，如果存在且为日期格式，转换为日期格式
    const processedFilters = { ...props.filters }
    if (processedFilters.shift_date && /^\d{4}-\d{2}-\d{2}$/.test(processedFilters.shift_date)) {
      // 保持 yyyy-MM-dd 格式
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(
      'organizational-management/handover-report/management-list/',
      requestParams,
    )
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0

    // 发送统计信息
    emit('statistics-change', {
      total_reports: data.total_count || 0,
      audited_reports: data.audited_count || 0,
      pending_reports: data.pending_count || 0,
      today_reports: data.today_count || 0,
    })
  } catch (error) {
    console.error('获取交接班管理列表失败:', error)
    ElMessage.error('获取交接班管理列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 处理行点击
const handleView = (row) => {
  emit('view', row)
}

// 处理编辑
const handleEdit = (row) => {
  emit('edit', row)
}

// 处理删除
const handleDelete = (row) => {
  emit('delete', row)
}

// 处理审核
const handleAudit = (row) => {
  emit('audit', row)
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 监听filters变化
watch(
  () => props.filters,
  () => {
    currentPage.value = 1
    loadData()
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  loadData,
})

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.handover-management-table-container {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.action-buttons .el-button {
  min-width: 60px;
  font-size: 12px;
  padding: 4px 8px;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #fdf2f8;
}
</style>
