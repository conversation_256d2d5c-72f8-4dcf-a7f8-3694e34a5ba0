<template>
  <el-dialog
    v-model="visible"
    title="交接班详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>交班编号：</label>
              <span class="font-mono">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>交班人员：</label>
              <span>{{ detailData.staff_name }}</span>
            </div>
            <div class="detail-item">
              <label>所属部门：</label>
              <span>{{ detailData.staff_department_name }}</span>
            </div>
            <div class="detail-item">
              <label>班次类型：</label>
              <el-tag :type="getShiftTypeTagType(detailData.schedule_shift_type)">
                {{ detailData.schedule_shift_type_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>交班日期：</label>
              <span>{{ detailData.schedule_date }}</span>
            </div>
            <div class="detail-item">
              <label>交班时间：</label>
              <span>{{ detailData.shift_time }}</span>
            </div>
          </div>
        </div>

        <!-- 工作总结 -->
        <div class="detail-section">
          <h3 class="section-title">工作总结</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            {{ detailData.work_summary || '无' }}
          </div>
        </div>

        <!-- 特殊情况跟进 -->
        <div class="detail-section">
          <h3 class="section-title">特殊情况跟进</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            {{ detailData.special_situation_follow_up || '无' }}
          </div>
        </div>

        <!-- 设备异常情况 -->
        <div class="detail-section">
          <h3 class="section-title">设备异常情况</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            <div v-if="detailData.equipment_abnormal_situation" class="text-orange-600">
              {{ detailData.equipment_abnormal_situation }}
            </div>
            <div v-else class="text-gray-500">无异常</div>
          </div>
        </div>

        <!-- 物品交接 -->
        <div class="detail-section">
          <h3 class="section-title">物品交接</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            {{ detailData.item_handover || '无' }}
          </div>
        </div>

        <!-- 其他重要事项 -->
        <div class="detail-section">
          <h3 class="section-title">其他重要事项</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            {{ detailData.other_important_notice || '无' }}
          </div>
        </div>

        <!-- 未完成工作 -->
        <div class="detail-section">
          <h3 class="section-title">未完成工作</h3>
          <div class="content-box p-4 bg-gray-50 rounded-lg">
            <div v-if="detailData.unfinished_work" class="text-red-600">
              {{ detailData.unfinished_work }}
            </div>
            <div v-else class="text-green-600">无未完成工作</div>
          </div>
        </div>

        <!-- 记录时间 -->
        <div class="detail-section">
          <h3 class="section-title">记录信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { getShiftTypeTagType } from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/handover-report/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取交接班详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.content-box {
  color: #374151;
  line-height: 1.625;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
