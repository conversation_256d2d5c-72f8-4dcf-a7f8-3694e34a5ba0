<template>
  <div class="maternal-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          孕产妇档案列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="uid" label="档案编号" min-width="160" fixed="left">
        <template #default="{ row }">
          <span class="font-mono">{{ row.uid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="phone" label="联系电话" min-width="130">
        <template #default="{ row }">
          <span class="font-mono text-gray-700">{{ row.phone }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="age" label="年龄" min-width="80">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.age }}岁</span>
        </template>
      </el-table-column>

      <el-table-column prop="gender_display" label="性别" min-width="80">
        <template #default="{ row }">
          <el-tag :type="row.gender === 1 ? 'primary' : 'danger'" size="small">
            {{ row.gender_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="blood_type" label="血型" min-width="80">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.blood_type || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="identity_number" label="身份证号" min-width="190">
        <template #default="{ row }">
          <span class="font-mono text-gray-600">{{ maskIdNumber(row.identity_number) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="emergency_contact" label="紧急联系人" min-width="150">
        <template #default="{ row }">
          <div class="text-sm">
            <div class="font-medium text-gray-800">{{ row.emergency_contact }}</div>
            <div class="text-gray-500">{{ row.emergency_contact_relation_display }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="emergency_contact_phone" label="紧急联系电话" min-width="140">
        <template #default="{ row }">
          <span class="font-mono text-gray-600">{{ row.emergency_contact_phone }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="建档时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.created_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="is_active" label="状态" min-width="80">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="180" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button @click.stop="handleEdit(row)" type="primary" size="small"> 编辑 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { List } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'

const emit = defineEmits(['edit', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'user/maternity/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '-'
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// 脱敏身份证号
const maskIdNumber = (idNumber) => {
  if (!idNumber) return '-'
  return idNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

// 转换API数据格式
const transformMaternalData = (apiData) => {
  return apiData.map((item) => washMaternalData(item))
}

const washMaternalData = (item) => {
  return {
    uid: item.uid,
    name: item.name,
    phone: item.phone,
    birth_date: item.birth_date,
    age: calculateAge(item.birth_date),
    ethnicity: item.ethnicity,
    native_place: item.native_place,
    gender: item.gender,
    gender_display: item.gender_display,
    blood_type: item.blood_type,
    home_address: item.home_address,
    is_active: item.is_active,
    identity_number: item.identity_number,
    emergency_contact: item.emergency_contact,
    emergency_contact_phone: item.emergency_contact_phone,
    emergency_contact_relation: item.emergency_contact_relation,
    emergency_contact_relation_display: item.emergency_contact_relation_display,
    created_at: item.created_at,
    updated_at: item.updated_at,
    last_login: item.last_login,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformMaternalData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取孕产妇档案列表失败:', error)
    ElMessage.error('获取孕产妇档案列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.maternal-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.maternal-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
