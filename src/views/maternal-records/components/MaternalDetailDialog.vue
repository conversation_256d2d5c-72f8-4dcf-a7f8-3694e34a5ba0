<template>
  <el-dialog
    v-model="dialogVisible"
    title="孕产妇档案详情"
    width="800px"
    align-center
    :before-close="handleClose"
    class="maternal-detail-dialog"
  >
    <div class="detail-content max-h-[70vh] overflow-y-auto">
      <template v-if="detailData">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>档案编号：</label>
                <span>{{ detailData.uid }}</span>
              </div>

              <div class="detail-item">
                <label>状态：</label>
                <el-tag :type="detailData.is_active ? 'success' : 'danger'" size="small">
                  {{ detailData.is_active ? '启用' : '禁用' }}
                </el-tag>
              </div>

              <div class="detail-item">
                <label>姓名：</label>
                <span>{{ detailData.name }}</span>
              </div>

              <div class="detail-item">
                <label>联系电话：</label>
                <span>{{ detailData.phone }}</span>
              </div>

              <div class="detail-item">
                <label>出生日期：</label>
                <span>{{ detailData.birth_date }} ({{ detailData.age }}岁)</span>
              </div>

              <div class="detail-item">
                <label>性别：</label>
                <el-tag :type="detailData.gender === 1 ? 'primary' : 'danger'" size="small">
                  {{ detailData.gender_display }}
                </el-tag>
              </div>

              <div class="detail-item">
                <label>血型：</label>
                <span>{{ detailData.blood_type || '-' }}</span>
              </div>

              <div class="detail-item">
                <label>身份证号：</label>
                <span>{{ detailData.identity_number }}</span>
              </div>

              <div class="detail-item">
                <label>民族：</label>
                <span>{{ detailData.ethnicity || '-' }}</span>
              </div>

              <div class="detail-item">
                <label>籍贯：</label>
                <span>{{ detailData.native_place || '-' }}</span>
              </div>
            </div>

            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
              <div class="detail-item">
                <label>家庭地址：</label>
                <span>{{ detailData.home_address || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 紧急联系人信息 -->
          <div class="detail-section">
            <h3 class="section-title">紧急联系人信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>紧急联系人：</label>
                <span>{{ detailData.emergency_contact }}</span>
              </div>

              <div class="detail-item">
                <label>联系电话：</label>
                <span>{{ detailData.emergency_contact_phone }}</span>
              </div>

              <div class="detail-item">
                <label>关系：</label>
                <span>{{ detailData.emergency_contact_relation_display }}</span>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="detail-section">
            <h3 class="section-title">系统信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>建档时间：</label>
                <span>{{ formatDateTime(detailData.created_at) }}</span>
              </div>

              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ formatDateTime(detailData.updated_at) }}</span>
              </div>

              <div class="detail-item">
                <label>最后登录：</label>
                <span>{{
                  detailData.last_login ? formatDateTime(detailData.last_login) : '-'
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleEdit"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑档案
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { format } from 'date-fns'

const emit = defineEmits(['update:modelValue', 'edit', 'close'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Object,
    default: null,
  },
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 计算详情数据，直接使用传入的rowData
const detailData = computed(() => {
  if (!props.rowData) return null

  return {
    ...props.rowData,
    // 如果rowData中已经有age字段就直接使用，否则计算
    age: props.rowData.age || calculateAge(props.rowData.birth_date),
  }
})

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '-'
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 事件处理
const handleEdit = () => {
  emit('edit', detailData.value)
}

const handleClose = () => {
  emit('close')
  dialogVisible.value = false
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
